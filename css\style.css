/*

Template 2075 Digital Team

http://www.tooplate.com/view/2075-digital-team

*/

body {
    background: #ffffff;
    font-family: 'Roboto', sans-serif;
    font-weight: 400;
    position: relative;
  }


html, body {
     width: 100%;
     overflow-x: hidden;
  }

h1, h2, h3, h4 {
  font-weight: bold;
  letter-spacing: 2px;
}

.heading {
  font-size: 20px;
}
.subheading {
  font-size: 14px;
  font-weight: bold;
  padding-bottom: 60px;
}
.bold {
  font-weight: bold;
}

p {
  line-height: 28px;
  font-size: 15px;
}

a {
  outline: none !important;
}

hr {
  width: 180px;
  border-color: #CCC;
}

.section-title strong {
	color: #00C6D7;
  	font-size: 56px;
}

.section-title h1 {
	font-size: 24px;
}

.section-title {
  padding-bottom: 32px;
}

.medium-icon {
  font-size: 32px !important;
}

#work,#about, #team, 
#portfolio, #pricing, #contact {
   padding-top: 100px;
  padding-bottom: 100px;
}


/* ==========================================================================
preloader section
========================================================================== */
.preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 99999;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-flow: row nowrap;
        -ms-flex-flow: row nowrap;
            flex-flow: row nowrap;
    -webkit-justify-content: center;
        -ms-flex-pack: center;
            justify-content: center;
    -webkit-align-items: center;
        -ms-flex-align: center;
            align-items: center;
    background: none repeat scroll 0 0 #fff;
}
.sk-spinner-circle.sk-spinner {
  width: 22px;
  height: 22px;
  position: relative; }
.sk-spinner-circle .sk-circle {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0; }
.sk-spinner-circle .sk-circle:before {
  content: '';
  display: block;
  margin: 0 auto;
  width: 20%;
  height: 20%;
  background-color: #00c6d7;
  border-radius: 100%;
  -webkit-animation: sk-circleBounceDelay 1.2s infinite ease-in-out;
          animation: sk-circleBounceDelay 1.2s infinite ease-in-out;
  /* Prevent first frame from flickering when animation starts */
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both; }
.sk-spinner-circle .sk-circle2 {
  -webkit-transform: rotate(30deg);
          transform: rotate(30deg); }
.sk-spinner-circle .sk-circle3 {
  -webkit-transform: rotate(60deg);
          transform: rotate(60deg); }
.sk-spinner-circle .sk-circle4 {
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg); }
.sk-spinner-circle .sk-circle5 {
  -webkit-transform: rotate(120deg);
          transform: rotate(120deg); }
.sk-spinner-circle .sk-circle6 {
  -webkit-transform: rotate(150deg);
          transform: rotate(150deg); }
.sk-spinner-circle .sk-circle7 {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg); }
.sk-spinner-circle .sk-circle8 {
  -webkit-transform: rotate(210deg);
          transform: rotate(210deg); }
.sk-spinner-circle .sk-circle9 {
  -webkit-transform: rotate(240deg);
          transform: rotate(240deg); }
.sk-spinner-circle .sk-circle10 {
  -webkit-transform: rotate(270deg);
          transform: rotate(270deg); }
.sk-spinner-circle .sk-circle11 {
  -webkit-transform: rotate(300deg);
          transform: rotate(300deg); }
.sk-spinner-circle .sk-circle12 {
  -webkit-transform: rotate(330deg);
          transform: rotate(330deg); }
.sk-spinner-circle .sk-circle2:before {
  -webkit-animation-delay: -1.1s;
          animation-delay: -1.1s; }
.sk-spinner-circle .sk-circle3:before {
  -webkit-animation-delay: -1s;
          animation-delay: -1s; }
.sk-spinner-circle .sk-circle4:before {
  -webkit-animation-delay: -0.9s;
          animation-delay: -0.9s; }
.sk-spinner-circle .sk-circle5:before {
  -webkit-animation-delay: -0.8s;
          animation-delay: -0.8s; }
.sk-spinner-circle .sk-circle6:before {
  -webkit-animation-delay: -0.7s;
          animation-delay: -0.7s; }
.sk-spinner-circle .sk-circle7:before {
  -webkit-animation-delay: -0.6s;
          animation-delay: -0.6s; }
.sk-spinner-circle .sk-circle8:before {
  -webkit-animation-delay: -0.5s;
          animation-delay: -0.5s; }
.sk-spinner-circle .sk-circle9:before {
  -webkit-animation-delay: -0.4s;
          animation-delay: -0.4s; }
.sk-spinner-circle .sk-circle10:before {
  -webkit-animation-delay: -0.3s;
          animation-delay: -0.3s; }
.sk-spinner-circle .sk-circle11:before {
  -webkit-animation-delay: -0.2s;
          animation-delay: -0.2s; }
.sk-spinner-circle .sk-circle12:before {
  -webkit-animation-delay: -0.1s;
          animation-delay: -0.1s; }

@-webkit-keyframes sk-circleBounceDelay {
  0%, 80%, 100% {
    -webkit-transform: scale(0);
            transform: scale(0); }

  40% {
    -webkit-transform: scale(1);
            transform: scale(1); } }

@keyframes sk-circleBounceDelay {
  0%, 80%, 100% {
    -webkit-transform: scale(0);
            transform: scale(0); }

  40% {
    -webkit-transform: scale(1);
            transform: scale(1); } }


/* ==========================================================================
navigation section
========================================================================== */
.custom-navbar {
    background: #4B4B4B;
    margin-bottom: 0;
}
.custom-navbar .navbar-brand  {
  color: #00c6d7;
  font-weight: 600;
  font-size: 24px;
}
.custom-navbar .navbar-brand .navbar-toggle {
    background: #00c6d7;
    padding: 4px 6px;
    font-size: 16px;
    color: #fff;
    }
.custom-navbar .nav li a {
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
    }
.custom-navbar .nav li a:hover {
  	background: transparent;
   color: #eeeeee;
}
.custom-navbar .nav > li > a:focus {
  background-color: transparent;
  color: #ffffff;
  outline: none;
}
.custom-navbar .nav li.active > a {
      color: #00c6d7;
    }
.custom-navbar .navbar-toggle {
        border: none;
        padding-top: 10px;
    }
.custom-navbar .navbar-toggle .icon-bar {
        background: #00c6d7;
        border-color: transparent;
    }
@media(min-width:768px){
    .custom-navbar {
            background: #4B4B4B;
            padding: 10px;
            border-bottom: 0;
            background: 0 0;
         }
    .custom-navbar.top-nav-collapse {
            padding: 8px;
            background: #4B4B4B;
        }
}


/* ==========================================================================
home section
========================================================================== */
#home {
  background-size: cover;
  background-position: center;
  color: #ffffff;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -ms-flex-align: center;
      align-items: center;
  height: 100vh;
  text-align: center;
}
#home h1 {
  font-size: 40px;
}
#home h3 {
  font-size: 14px;
}
#home hr {
  width: 240px;
}
#home .btn {
  background: transparent;
  border: 4px solid #ffffff;
  border-radius: 0;
  color: #ffffff;
  font-size: 15px;
  font-weight: bold;
  letter-spacing: 2px;
  padding: 12px 42px;
  margin-top: 40px;
  margin-right: 16px;
  transition: all 0.4s ease-in-out;
}
#home .btn:hover {
  background: #00c6d7;
  border-color: transparent;
}
#home .btn-danger {
  background: #00c6d7;
  border-color: transparent;
}
#home .btn-danger:hover {
	border: 4px solid #ffffff;
  	border-radius: 0;
	background: transparent;
  	color: #ffffff;
}

/* ==========================================================================
work section
========================================================================== */
#work {
    text-align: center;
}
#work .col-md-4 {
  padding: 28px;
  position: relative;
  transition: all 0.4s ease-in-out;
}
#work .col-md-4:hover {
  background-color: #ffffff;
  box-shadow: 0px 2px 8px 0px rgba(50, 50, 50, 0.20);
}
#work h3 {
  font-size: 18px;
}


/* ==========================================================================
about section
========================================================================== */
#about {
  background: #3B3B3B;
  color: #ffffff;
}
.nav-tabs {
  margin-top: 20px;
  border-bottom: 1px solid #606060;
}
.nav-tabs > li {
  float: left;
  margin-bottom: -1px;
}
.nav-tabs > li > a {
	display: block;
  	color: #ffffff;
  	font-size: 16px;
  	padding: 10px 20px;
  	border: 0;
  	border-radius: 0;
}
.nav-tabs > li > a:hover {
  background-color: transparent;
}
.nav-tabs > li.active > a,
.nav-tabs > li.active > a:hover,
.nav-tabs > li.active > a:focus {
  color: #00c6d7;
  cursor: default;
  background-color: transparent;
  border: 1px solid transparent;
  border-bottom-color: transparent;
}
.tab-content {
  padding-top: 20px;
}

.tab-content a {
	color: #0CC;
}

.tab-content a:hover, .tab-content a:link {
	color: #FF0;
	text-decoration: none;
}

/* ==========================================================================
team section
========================================================================== */
#team {
  text-align: center;
}
#team h3 {
  color: #00c6d7;
  font-size: 14px;
  font-weight: bold;
}
#team .col-md-3 {
  padding-top: 20px;
  padding-bottom: 20px;
}
#team .team-wrapper {
  overflow: hidden;
  position: relative;
}
#team .team-des {
  background-color: rgba(00,00,00,0.6);
  position: absolute;
  color: #ffffff;
  opacity: 0;
  max-width: 500px;
  width: 100%;
  height: 100%;
  text-align: center;
  vertical-align: middle;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transition: all 0.4s ease-in-out;
  margin: 0 auto;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
#team .team-des:hover {
 opacity: 1;
}
#team img {
  margin: 0 auto;
}

#team .social-icon li a {
  color: #fff;
}

#team .social-icon li a:hover {
  color: #00c6d7;
}

/* ==========================================================================
portfolio section
========================================================================== */
#portfolio {
  background-color: #f6f6f6;
  text-align: center;
  }
#portfolio .container {
  width: 100%;
  padding: 0;
  margin: 0;
}
#portfolio img {
  width: 100%;
  transition: all 0.4s ease-in-out;
}
#portfolio img:hover {
  opacity: 0.5;
}
/* FILTER CSS */
.filter-wrapper {
    width: 100%;
    margin: 40px 0 24px 0;
    overflow: hidden;
    text-align: center;
}
.filter-wrapper li {
    display: inline-block;
    margin: 4px;
}
.filter-wrapper li a {
    color: #999999;
    font-size: 13px;
    font-weight: bold;
    letter-spacing: 2px;
    text-transform: uppercase;
    padding: 8px 17px;
    display: block;
    text-decoration: none;
    transition: all 0.4s ease-in-out;
}
.filter-wrapper li .selected,
.filter-wrapper li a:focus,
.filter-wrapper li a:hover {
    color: #00c6d7;
    outline: none;
  }

/* ISOTOPE BOX CSS */
.iso-box-section {
    width: 100%;
}
.iso-box-wrapper {
    width: 100%;
    padding: 0;
    clear: both;
    position: relative;
}
.iso-box {
    position: relative;
    min-height: 50px;
    float: left;
    overflow: hidden;
    margin-bottom: 20px;
}
.iso-box > a {
    display: block;
    width: 100%;
    height: 100%;
    overflow: hidden;
}
.iso-box > a {
    display: block;
    width: 100%;
    height: 100%;
    overflow: hidden;
}
.fluid-img {
  width: 100%;
  display: block;
}

/* ==========================================================================
pricing section
========================================================================== */
#pricing {
  text-align: center;
}
#pricing h2 {
  font-size: 52px;
}
#pricing h3 {
  font-size: 16px;
}
#pricing .plan {
  box-shadow: 0px 2px 8px 0px rgba(50, 50, 50, 0.20);
  padding-top: 40px;
  padding-bottom: 80px;
  margin-bottom: 32px;
}
#pricing .plan span {
	font-size: 16px;
}
#pricing .plan .plan_title {
  font-weight: 300;
  letter-spacing: 2px;
  padding: 30px;
  margin-bottom: 20px;
}
#pricing .medium-icon {
  box-shadow: 0px 2px 8px 0px rgba(50, 50, 50, 0.20);
  border-radius: 50%;
  font-size: 52px;
  width: 120px;
  height: 120px;
  line-height: 120px;
  text-align: center;
  margin-bottom: 32px;
}
#pricing .plan ul {
  padding: 0px;
}
#pricing .plan ul li {
  display: block;
  font-weight: 500;
  padding: 12px 0px;
}
#pricing .plan .btn {
  border: none;
  border-radius: 0px;
  color: #ffffff;
  font-size: 16px;
  font-weight: bold;
  padding: 16px 60px;
  transition: all 0.4s ease-in-out;
  margin-top: 20px;
  text-transform: uppercase;
}
#pricing .plan-one .btn {
  background-color: #00c6d7;
}
#pricing .plan-two .btn {
  background-color: #ffa400;
}
#pricing .plan-three .btn {
  background-color: #009988;
}
#pricing .plan .btn:hover {
  background-color: #3B3B3B;
}


/* ==========================================================================
contact section
========================================================================== */
#contact {
  background: #3B3B3B url('../images/contact-bg.jpg') no-repeat center center;
  background-size: cover;
  color: #ffffff;
}
#contact h2 {
  padding-bottom: 20px;
}
#contact h3 {
  font-size: 16px;
  font-weight: bold;
  letter-spacing: 1px;
}
#contact .medium-icon {
  padding-top: 10px;
  padding-right: 10px;
}
#contact .contact-info div {
  padding-top: 14px;
  padding-bottom: 14px;
}
#contact form {
  padding-top: 60px;
}
#contact .form-control {
  border: 2px solid #ffffff;
  border-radius: 0;
  box-shadow: none;
  margin-top: 10px;
  margin-bottom: 10px;
  transition: all 0.4s ease-in-out;
}
#contact input {
  height: 40px;
}
#contact input[type="submit"] {
  background: #00c6d7;
  border: 3px solid #00c6d7;
  letter-spacing: 1px;
  margin-top: 18px;
  height: 50px;
  color: #ffffff;
}
#contact input[type="submit"]:hover {
  color: #000000;
}


/* ==========================================================================
footer section
========================================================================== */
footer {
  text-align: center;
  padding-top: 60px;
  padding-bottom: 60px;
}
footer p {
  padding-top: 20px;
}
.social-icon {
  padding: 0;
  margin: 0;
}
.social-icon li {
  list-style: none;
  display: inline-block;
  padding: 6px;
}
.social-icon li a {
  box-shadow: 0px 2px 8px 0px rgba(50, 50, 50, 0.09);
  color: #808080;
  font-size: 20px;
  width: 56px;
  height: 56px;
  line-height: 60px;
  text-align: center;
  text-decoration: none;
  transition: all 0.4s ease-in-out;
}
.social-icon li a:hover {
  color: #00c6d7;
}

@media screen and (min-width: 992px) {
  @-moz-document url-prefix() {
    #portfolio .col-lg-4,
    #portfolio .col-md-4 {
      width: 33.333%;
    }
  }    
}

/* ==========================================================================
media quires for mobile
========================================================================== */
@media (max-width: 440px) {
  #home {
    padding-top: 320px;
    padding-bottom: 320px;
  }
  #home h1 {
    font-size: 22px;
  }
  #home h3 {
    font-size: 12px;
  }
}

/* 懸浮按鈕樣式 */
.floating-buttons {
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 9999;
  pointer-events: none;
}

.floating-buttons .floating-btn {
  pointer-events: auto;
}

.floating-btn {
  display: block !important;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-bottom: 10px;
  text-align: center;
  line-height: 60px;
  font-size: 24px;
  color: white !important;
  text-decoration: none;
  box-shadow: 0 4px 8px rgba(0,0,0,0.3);
  transition: all 0.3s ease;
  animation: pulse 2s infinite;
  position: relative;
  z-index: 10000;
}

.floating-btn:hover {
  transform: scale(1.1);
  color: white;
  text-decoration: none;
}

.floating-btn.phone {
  background-color: #28a745;
}

.floating-btn.line {
  background-color: #00c300;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
  }
}

/* 手機版調整 */
@media (max-width: 768px) {
  .floating-buttons {
    right: 15px;
    bottom: 15px;
  }

  .floating-btn {
    width: 50px;
    height: 50px;
    line-height: 50px;
    font-size: 20px;
  }
}

/* 最新消息樣式 */
.news-article {
  margin-bottom: 30px;
}

.news-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
}

.news-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.pinned-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background: #dc3545;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  z-index: 2;
}

.news-image {
  height: 200px;
  overflow: hidden;
}

.news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-content {
  padding: 20px;
}

.news-meta {
  margin-bottom: 10px;
}

.news-date {
  color: #666;
  font-size: 14px;
  margin-right: 10px;
}

.news-tag {
  background: #007bff;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  margin-right: 5px;
  display: inline-block;
}

.news-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #333;
  line-height: 1.4;
}

.news-excerpt {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.news-read-more {
  color: #007bff;
  text-decoration: none;
  font-weight: bold;
  transition: color 0.3s ease;
}

.news-read-more:hover {
  color: #0056b3;
  text-decoration: none;
}

/* 新聞模態框樣式 */
.news-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.8);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.news-modal-content {
  background: white;
  border-radius: 8px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.news-modal-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.news-modal-header h2 {
  margin: 0;
  color: #333;
}

.news-modal-close {
  font-size: 24px;
  cursor: pointer;
  color: #999;
  transition: color 0.3s ease;
}

.news-modal-close:hover {
  color: #333;
}

.news-modal-body {
  padding: 20px;
}

.news-full-content {
  line-height: 1.8;
}

.news-loading {
  text-align: center;
  padding: 40px;
  color: #666;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .news-modal {
    padding: 10px;
  }

  .news-modal-content {
    max-height: 95vh;
  }

  .news-modal-header,
  .news-modal-body {
    padding: 15px;
  }

  .news-card {
    margin-bottom: 20px;
  }
}

/* Logo 樣式 */
.navbar-brand {
  display: flex !important;
  align-items: center;
  padding: 10px 15px !important;
  height: auto !important;
}

.logo-img {
  height: 40px;
  width: auto;
  margin-right: 10px;
  transition: transform 0.3s ease;
  /* 優化透明背景logo顯示 */
  max-width: 120px;
  object-fit: contain;
}

.brand-text {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  white-space: nowrap;
}

.navbar-brand:hover .logo-img {
  transform: scale(1.05);
}

.navbar-brand:hover .brand-text {
  color: #007bff;
  text-decoration: none;
}

/* 響應式 Logo */
@media (max-width: 768px) {
  .logo-img {
    height: 35px;
    margin-right: 8px;
  }

  .brand-text {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .logo-img {
    height: 30px;
    margin-right: 6px;
  }

  .brand-text {
    font-size: 14px;
  }
}

/* 頁尾 Logo 樣式 */
.footer-logo {
  text-align: center;
  margin-bottom: 20px;
}

.footer-logo-img {
  height: 60px;
  width: auto;
  opacity: 0.9;
  transition: opacity 0.3s ease, transform 0.3s ease;
  /* 透明背景logo優化 */
  max-width: 150px;
  object-fit: contain;
}

.footer-logo-img:hover {
  opacity: 1;
  transform: scale(1.05);
}

/* 響應式頁尾 Logo */
@media (max-width: 768px) {
  .footer-logo-img {
    height: 50px;
  }
}

@media (max-width: 480px) {
  .footer-logo-img {
    height: 40px;
  }
}